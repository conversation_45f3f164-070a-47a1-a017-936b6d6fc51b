<?php

declare(strict_types=1);

use App\Http\Controllers\AdminCourseRegistrationController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Admin Course Registrations resource routes
    Route::prefix('admin-course-registrations')->name('admin-course-registrations.')->group(function () {
        Route::get('/', [AdminCourseRegistrationController::class, 'index'])
            ->middleware('can:view_course')
            ->name('index');

        Route::get('/create', [AdminCourseRegistrationController::class, 'create'])
            ->middleware('can:create_course')
            ->name('create');

        Route::post('/', [AdminCourseRegistrationController::class, 'store'])
            ->middleware('can:create_course')
            ->name('store');

        Route::get('/{adminCourseRegistration}', [AdminCourseRegistrationController::class, 'show'])
            ->middleware('can:view_course')
            ->name('show');

        Route::delete('/{adminCourseRegistration}', [AdminCourseRegistrationController::class, 'destroy'])
            ->middleware('can:delete_course')
            ->name('destroy');

        Route::patch('/{adminCourseRegistration}/drop', [AdminCourseRegistrationController::class, 'drop'])
            ->middleware('can:edit_course')
            ->name('drop');

        Route::patch('/{adminCourseRegistration}/withdraw', [AdminCourseRegistrationController::class, 'withdraw'])
            ->middleware('can:edit_course')
            ->name('withdraw');
    });

    // API routes for course registration operations
    Route::prefix('api/admin-course-registrations')->name('api.admin-course-registrations.')->group(function () {
        Route::delete('/bulk-delete', [AdminCourseRegistrationController::class, 'bulkDestroy'])
            ->middleware('can:delete_course')
            ->name('bulk-delete');

        Route::get('/available-courses', [AdminCourseRegistrationController::class, 'getAvailableCourses'])
            ->middleware('can:view_course')
            ->name('available-courses');

        Route::get('/check-eligibility', [AdminCourseRegistrationController::class, 'checkEligibility'])
            ->middleware('can:view_course')
            ->name('check-eligibility');

        Route::get('/student-registrations', [AdminCourseRegistrationController::class, 'getStudentRegistrations'])
            ->middleware('can:view_course')
            ->name('student-registrations');
    });
});
